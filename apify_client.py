import requests
from typing import Dict, List, Any

class ApifyClient:
    """A simple and stable Apify API client."""
    
    def __init__(self, api_token: str):
        if not api_token or not api_token.strip():
            raise ValueError("API token cannot be empty")

        self.api_token = api_token.strip()
        self.base_url = "https://api.apify.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """Tests the API token by fetching user data."""
        try:
            response = requests.get(
                f"{self.base_url}/users/me",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def list_actors(self) -> Dict[str, Any]:
        """Gets a list of the user's actors."""
        try:
            response = requests.get(f"{self.base_url}/acts", headers=self.headers, timeout=10)
            if response.status_code == 200:
                items = response.json().get("data", {}).get("items", [])
                return {"success": True, "actors": items}
            return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_actor_details(self, actor_id: str) -> Dict[str, Any]:
        """Gets details for an actor, including its input schema."""
        try:
            response = requests.get(f"{self.base_url}/acts/{actor_id}", headers=self.headers, timeout=10)
            if response.status_code != 200:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
            
            actor_data = response.json().get("data", {})

            # Safely try to add the input schema
            try:
                version_res = requests.get(
                    f"{self.base_url}/acts/{actor_id}/versions/latest",
                    headers=self.headers,
                    timeout=10
                )
                if version_res.status_code == 200:
                    input_schema = version_res.json().get("data", {}).get("inputSchema")
                    if input_schema:
                        actor_data["inputSchema"] = input_schema
            except Exception:
                pass  # Failing to get schema is not a critical error

            return {"success": True, "actor": actor_data}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def run_actor(self, actor_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Runs an actor and waits for completion."""
        try:
            response = requests.post(
                f"{self.base_url}/acts/{actor_id}/runs",
                headers=self.headers,
                json=input_data,
                params={"waitForFinish": 120},
                timeout=130
            )
            
            if response.status_code == 201:
                run_data = response.json().get("data", {})
                return {"success": True, "run": run_data}
            return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_run_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """Gets dataset items from a completed run."""
        try:
            response = requests.get(f"{self.base_url}/datasets/{dataset_id}/items", headers=self.headers, timeout=10)
            if response.status_code == 200:
                return {"success": True, "items": response.json()}
            return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}