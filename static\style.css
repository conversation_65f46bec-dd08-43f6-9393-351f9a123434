* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    background: #0f0f23;
    color: #cccccc;
    line-height: 1.6;
    min-height: 100vh;
}

.app {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 3rem;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    color: #00ff41;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
}

header p {
    color: #666;
    font-size: 1.1rem;
}

.step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.input-group {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

input {
    flex: 1;
    padding: 1rem;
    background: #1a1a2e;
    border: 1px solid #333;
    border-radius: 8px;
    color: #fff;
    font-size: 1rem;
}

input:focus {
    outline: none;
    border-color: #00ff41;
    box-shadow: 0 0 0 2px rgba(0, 255, 65, 0.1);
}

input::placeholder {
    color: #666;
}

button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.primary {
    background: #00ff41;
    color: #000;
}

.primary:hover {
    background: #00cc33;
    transform: translateY(-1px);
}

.secondary {
    background: #333;
    color: #fff;
}

.secondary:hover {
    background: #444;
}

.hidden {
    display: none !important;
}

.status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.status.success {
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
    border: 1px solid rgba(0, 255, 65, 0.3);
}

.status.error {
    background: rgba(255, 0, 0, 0.1);
    color: #ff4444;
    border: 1px solid rgba(255, 0, 0, 0.3);
}

h2 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
    color: #fff;
    font-weight: 300;
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.actor-card {
    background: #1a1a2e;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.actor-card:hover {
    border-color: #00ff41;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.actor-card h3 {
    color: #fff;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.actor-card p {
    color: #999;
    font-size: 0.9rem;
    line-height: 1.4;
}

.actor-header {
    text-align: center;
    margin-bottom: 2rem;
}

.actor-header h2 {
    color: #00ff41;
    margin-bottom: 0.5rem;
}

.actor-header p {
    color: #999;
}

.form {
    max-width: 600px;
    margin: 0 auto;
}

.field-group {
    margin-bottom: 1.5rem;
}

.field-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 500;
}

.field-group input,
.field-group select,
.field-group textarea {
    width: 100%;
    padding: 1rem;
    background: #1a1a2e;
    border: 1px solid #333;
    border-radius: 8px;
    color: #fff;
    font-size: 1rem;
}

.field-group textarea {
    resize: vertical;
    min-height: 100px;
}

.actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.results-header {
    text-align: center;
    margin-bottom: 2rem;
}

.run-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.run-status.success {
    background: rgba(0, 255, 65, 0.2);
    color: #00ff41;
}

.run-status.failed {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

.results {
    background: #1a1a2e;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    max-height: 500px;
    overflow-y: auto;
}

.results pre {
    color: #ccc;
    font-family: 'SF Mono', Monaco, monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
}

.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 35, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loader.active {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #333;
    border-top: 3px solid #00ff41;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .app {
        padding: 1rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .grid {
        grid-template-columns: 1fr;
    }
    
    .actions {
        flex-direction: column;
    }
}
