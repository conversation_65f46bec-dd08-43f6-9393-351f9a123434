let token = '';
let actor = null;

function showLoader() {
    document.getElementById('loader').classList.add('active');
}

function hideLoader() {
    document.getElementById('loader').classList.remove('active');
}

function showStep(stepId) {
    document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
    document.getElementById(stepId).classList.add('active');
}

function showStatus(elementId, message, isError = false) {
    const el = document.getElementById(elementId);
    el.textContent = message;
    el.className = `status ${isError ? 'error' : 'success'}`;
}

async function authenticate() {
    const input = document.getElementById('api-token');
    token = input.value.trim();
    
    if (!token) {
        showStatus('auth-status', 'Please enter your API token', true);
        return;
    }
    
    showLoader();
    
    try {
        const r = await fetch('/api/test-token', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({api_token: token})
        });
        
        const data = await r.json();
        
        if (data.success) {
            showStatus('auth-status', 'Connected successfully!');
            setTimeout(() => loadActors(), 1000);
        } else {
            showStatus('auth-status', data.error, true);
        }
    } catch (e) {
        showStatus('auth-status', 'Connection failed', true);
    } finally {
        hideLoader();
    }
}

async function loadActors() {
    showStep('actors-step');
    showLoader();
    
    try {
        const r = await fetch('/api/actors', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({api_token: token})
        });
        
        const data = await r.json();
        
        if (data.success) {
            displayActors(data.actors);
        } else {
            showStatus('actors-status', data.error, true);
        }
    } catch (e) {
        showStatus('actors-status', 'Failed to load actors', true);
    } finally {
        hideLoader();
    }
}

function displayActors(actors) {
    const grid = document.getElementById('actors-grid');
    grid.innerHTML = '';
    
    actors.forEach(a => {
        const card = document.createElement('div');
        card.className = 'actor-card';
        card.onclick = () => selectActor(a);
        
        card.innerHTML = `
            <h3>${a.name || 'Unnamed'}</h3>
            <p>${a.description || 'No description'}</p>
        `;
        
        grid.appendChild(card);
    });
}

function selectActor(a) {
    actor = a;
    loadSchema(a.id);
}

async function loadSchema(actorId) {
    showStep('config-step');
    showLoader();
    
    document.getElementById('actor-name').textContent = actor.name || 'Unnamed';
    document.getElementById('actor-desc').textContent = actor.description || '';
    
    try {
        const r = await fetch(`/api/actor/${actorId}/schema`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({api_token: token})
        });
        
        const data = await r.json();
        
        if (data.success) {
            buildForm(data.schema);
        } else {
            showStatus('config-status', data.error, true);
        }
    } catch (e) {
        showStatus('config-status', 'Failed to load schema', true);
    } finally {
        hideLoader();
    }
}

function buildForm(schema) {
    const fields = document.getElementById('form-fields');
    fields.innerHTML = '';
    
    if (!schema?.properties) {
        fields.innerHTML = '<p>No configuration needed</p>';
        return;
    }
    
    Object.entries(schema.properties).forEach(([key, field]) => {
        const group = document.createElement('div');
        group.className = 'field-group';
        
        const label = document.createElement('label');
        label.textContent = field.title || key;
        if (schema.required?.includes(key)) label.textContent += ' *';
        
        let input;
        
        if (field.enum) {
            input = document.createElement('select');
            field.enum.forEach(opt => {
                const option = document.createElement('option');
                option.value = option.textContent = opt;
                input.appendChild(option);
            });
        } else if (field.type === 'boolean') {
            input = document.createElement('input');
            input.type = 'checkbox';
        } else if (field.type === 'number') {
            input = document.createElement('input');
            input.type = 'number';
        } else {
            input = document.createElement('input');
            input.type = 'text';
        }
        
        input.name = key;
        if (field.default !== undefined) {
            if (field.type === 'boolean') {
                input.checked = field.default;
            } else {
                input.value = field.default;
            }
        }
        
        group.appendChild(label);
        group.appendChild(input);
        fields.appendChild(group);
    });
}

async function execute() {
    if (!actor) return;
    
    showLoader();
    
    const form = document.getElementById('config-form');
    const data = new FormData(form);
    const input = {};
    
    for (let [key, value] of data.entries()) {
        const field = form.querySelector(`[name="${key}"]`);
        if (field.type === 'checkbox') {
            input[key] = field.checked;
        } else if (field.type === 'number') {
            input[key] = parseFloat(value) || 0;
        } else {
            input[key] = value;
        }
    }
    
    try {
        const r = await fetch(`/api/actor/${actor.id}/run`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({api_token: token, input})
        });
        
        const result = await r.json();
        
        if (result.success) {
            showResults(result.run, result.results);
        } else {
            showStatus('config-status', result.error, true);
        }
    } catch (e) {
        showStatus('config-status', 'Execution failed', true);
    } finally {
        hideLoader();
    }
}

function showResults(run, results) {
    showStep('results-step');
    
    const status = document.getElementById('run-status');
    status.textContent = run.status;
    status.className = `run-status ${run.status === 'SUCCEEDED' ? 'success' : 'failed'}`;
    
    const content = document.getElementById('results-content');
    content.innerHTML = `<pre>${JSON.stringify(results || 'No data', null, 2)}</pre>`;
}

function goBack() {
    showStep('actors-step');
}

function restart() {
    token = '';
    actor = null;
    document.getElementById('api-token').value = '';
    showStep('auth-step');
}

document.getElementById('api-token').addEventListener('keypress', e => {
    if (e.key === 'Enter') authenticate();
});
