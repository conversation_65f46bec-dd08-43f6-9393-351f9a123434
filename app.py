from flask import Flask, render_template, request, jsonify
from apify_client import ApifyClient
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/test-token', methods=['POST'])
def test_token():
    """Test if the provided API token is valid"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "Invalid JSON data"}), 400

        api_token = data.get('api_token')

        if not api_token or not api_token.strip():
            return jsonify({"success": False, "error": "API token is required"}), 400

        client = ApifyClient(api_token.strip())
        result = client.test_connection()

        if result["success"]:
            return jsonify({"success": True, "message": "Token is valid"})
        else:
            return jsonify({"success": False, "error": result["error"]}), 401
    except Exception as e:
        return jsonify({"success": False, "error": f"Server error: {str(e)}"}), 500

@app.route('/api/actors', methods=['POST'])
def get_actors():
    """Get list of user's actors"""
    data = request.get_json()
    api_token = data.get('api_token')

    if not api_token:
        return jsonify({"success": False, "error": "API token is required"}), 400

    client = ApifyClient(api_token)
    result = client.list_actors()

    if result["success"]:
        return jsonify({"success": True, "actors": result["actors"]})
    else:
        return jsonify({"success": False, "error": result["error"]}), 400

@app.route('/api/actor/<actor_id>/schema', methods=['POST'])
def get_actor_schema(actor_id):
    """Get actor input schema"""
    data = request.get_json()
    api_token = data.get('api_token')

    if not api_token:
        return jsonify({"success": False, "error": "API token is required"}), 400

    client = ApifyClient(api_token)
    result = client.get_actor_details(actor_id)

    if result["success"]:
        actor = result["actor"]
        input_schema = actor.get("input", {})
        return jsonify({
            "success": True,
            "schema": input_schema,
            "name": actor.get("name", "Unknown"),
            "description": actor.get("description", "")
        })
    else:
        return jsonify({"success": False, "error": result["error"]}), 400

@app.route('/api/actor/<actor_id>/run', methods=['POST'])
def run_actor(actor_id):
    """Run an actor with provided input"""
    data = request.get_json()
    api_token = data.get('api_token')
    input_data = data.get('input', {})

    if not api_token:
        return jsonify({"success": False, "error": "API token is required"}), 400

    client = ApifyClient(api_token)
    result = client.run_actor(actor_id, input_data)

    if result["success"]:
        run_data = result["run"]

        # Get dataset results if available
        dataset_id = run_data.get("defaultDatasetId")
        dataset_items = []

        if dataset_id:
            dataset_result = client.get_run_dataset(dataset_id)
            if dataset_result["success"]:
                dataset_items = dataset_result["items"]

        return jsonify({
            "success": True,
            "run": run_data,
            "results": dataset_items
        })
    else:
        return jsonify({"success": False, "error": result["error"]}), 400

if __name__ == '__main__':
    app.run(debug=True, port=5000)
