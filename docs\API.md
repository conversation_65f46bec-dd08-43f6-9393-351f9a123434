# API Documentation

This document describes the REST API endpoints provided by the Apify Actor Runner application.

## Base URL

```
http://localhost:5000
```

## Authentication

All API endpoints (except the main page) require an Apify API token to be included in the request body.

## Endpoints

### GET /

**Description**: Serves the main application interface

**Response**: HTML page

---

### POST /api/test-token

**Description**: Validates an Apify API token

**Request Body**:
```json
{
  "api_token": "string"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Token is valid"
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error message"
}
```

**Status Codes**:
- `200`: Token is valid
- `400`: Invalid request or missing token
- `401`: Invalid token
- `500`: Server error

---

### POST /api/actors

**Description**: Retrieves list of user's actors

**Request Body**:
```json
{
  "api_token": "string"
}
```

**Response**:
```json
{
  "success": true,
  "actors": [
    {
      "id": "actor_id",
      "name": "Actor Name",
      "description": "Actor description",
      "username": "username",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "modifiedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error message"
}
```

**Status Codes**:
- `200`: Success
- `400`: Invalid request or API error
- `500`: Server error

---

### POST /api/actor/{actor_id}/schema

**Description**: Retrieves actor's input schema

**URL Parameters**:
- `actor_id`: The ID of the actor

**Request Body**:
```json
{
  "api_token": "string"
}
```

**Response**:
```json
{
  "success": true,
  "schema": {
    "type": "object",
    "properties": {
      "field_name": {
        "type": "string",
        "title": "Field Title",
        "description": "Field description",
        "default": "default_value"
      }
    },
    "required": ["field_name"]
  },
  "name": "Actor Name",
  "description": "Actor description"
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error message"
}
```

**Status Codes**:
- `200`: Success
- `400`: Invalid request or API error
- `500`: Server error

---

### POST /api/actor/{actor_id}/run

**Description**: Runs an actor with provided input

**URL Parameters**:
- `actor_id`: The ID of the actor to run

**Request Body**:
```json
{
  "api_token": "string",
  "input": {
    "field1": "value1",
    "field2": 123,
    "field3": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "run": {
    "id": "run_id",
    "actId": "actor_id",
    "status": "SUCCEEDED",
    "startedAt": "2023-01-01T00:00:00.000Z",
    "finishedAt": "2023-01-01T00:00:01.000Z",
    "stats": {
      "runTimeSecs": 1.5
    },
    "defaultDatasetId": "dataset_id"
  },
  "results": [
    {
      "key": "value",
      "data": "extracted_data"
    }
  ]
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error message"
}
```

**Status Codes**:
- `200`: Success
- `400`: Invalid request or API error
- `500`: Server error

## Error Handling

All endpoints return consistent error responses with the following structure:

```json
{
  "success": false,
  "error": "Human-readable error message"
}
```

Common error scenarios:
- **Missing API token**: `400` with message about required token
- **Invalid API token**: `401` with authentication error
- **Actor not found**: `400` with actor-specific error
- **Network issues**: `500` with connection error
- **Invalid input**: `400` with validation error

## Rate Limiting

The application respects Apify's API rate limits:
- Global: 250,000 requests per minute
- Per-resource: 30 requests per second (default)
- Actor runs: 200 requests per second

## Input Schema Types

The application supports all JSON Schema types:

### String
```json
{
  "type": "string",
  "title": "Text Field",
  "description": "Enter some text"
}
```

### Number/Integer
```json
{
  "type": "number",
  "title": "Numeric Field",
  "description": "Enter a number"
}
```

### Boolean
```json
{
  "type": "boolean",
  "title": "Checkbox Field",
  "description": "Check if true"
}
```

### Array
```json
{
  "type": "array",
  "title": "List Field",
  "description": "Enter comma-separated values"
}
```

### Enum (Select)
```json
{
  "type": "string",
  "enum": ["option1", "option2", "option3"],
  "title": "Select Field",
  "description": "Choose an option"
}
```

## Security Notes

- API tokens are never stored server-side
- All requests use HTTPS in production
- Input validation is performed on both client and server
- Error messages don't expose sensitive information
