<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Actor Runner</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>Actor Runner</h1>
        <p>Run your Apify actors with a simple web interface</p>
        
        <!--API Token-->
        <div class="step" id="step-token">
            <h2>Step 1: Enter your Apify API Token</h2>
            <div class="form-group">
                <input type="password" id="api-token" placeholder="Enter your Apify API token">
                <button onclick="validateToken()">Validate Token</button>
            </div>
            <div id="token-status" class="status"></div>
        </div>
        
        <!--Select Actor-->
        <div class="step hidden" id="step-actors">
            <h2>Step 2: Select an Actor</h2>
            <div id="actors-list" class="actors-grid"></div>
            <div id="actors-status" class="status"></div>
        </div>
        
        <!--Configure Input-->
        <div class="step hidden" id="step-input">
            <h2>Step 3: Configure Actor Input</h2>
            <div id="actor-info">
                <h3 id="selected-actor-name"></h3>
                <p id="selected-actor-description"></p>
            </div>
            <form id="input-form">
                <div id="input-fields"></div>
                <button type="submit">Run Actor</button>
            </form>
            <div id="input-status" class="status"></div>
        </div>
        
        <!--Results-->
        <div class="step hidden" id="step-results">
            <h2>Step 4: Results</h2>
            <div id="run-info"></div>
            <div id="results-container">
                <h3>Dataset Results:</h3>
                <pre id="results-data"></pre>
            </div>
            <button onclick="startOver()">Start Over</button>
        </div>
        
        <!--Loading Indicator-->
        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
