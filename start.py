
import sys
import subprocess
import os

def check_files():
    """Check if required files exist"""
    required_files = [
        "app.py",
        "apify_client.py", 
        "requirements.txt",
        "templates/index.html",
        "static/style.css",
        "static/script.js"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required files present")
    return True

def start_application():
    """Start the Flask application"""
    print("🚀 Starting Apify Actor Runner...")
    print("   Application will be available at: http://localhost:5000")
    print("   Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        subprocess.call([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped")

def main():
    """Main function"""
    print("\nWelcome to Apify Actor Runner - Quick Start")
    print("=" * 40)
    
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Get your Apify API token from: https://console.apify.com/account#/integrations")
    print("2. Open http://localhost:5000 in your browser")
    print("3. Enter your API token and start running actors!")
    print()
    
    # Ask user if they want to start the app
    try:
        response = input("Start the application now? (y/n): ").lower().strip()
        if response in ['y', 'yes', '']:
            start_application()
        else:
            print("👍 Run 'python app.py' when you're ready to start")
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
