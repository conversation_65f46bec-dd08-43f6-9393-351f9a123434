<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Apify Actor Runner</title>
    <style>
        body { font-family: <PERSON>l, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Debug Page - Apify Actor Runner</h1>
    
    <div class="test">
        <h3>1. Basic JavaScript Test</h3>
        <button onclick="testBasicJS()">Test Basic JavaScript</button>
        <div id="js-test-result"></div>
    </div>
    
    <div class="test">
        <h3>2. DOM Elements Test</h3>
        <button onclick="testDOMElements()">Test DOM Elements</button>
        <div id="dom-test-result"></div>
    </div>
    
    <div class="test">
        <h3>3. API Connection Test</h3>
        <input type="text" id="test-token" placeholder="Enter API token for testing">
        <button onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-test-result"></div>
    </div>
    
    <div class="test">
        <h3>4. Console Log</h3>
        <p>Open browser developer tools (F12) and check the Console tab for any error messages.</p>
        <button onclick="console.log('Test console message')">Test Console Log</button>
    </div>
    
    <div class="test">
        <h3>5. Main App Link</h3>
        <a href="http://localhost:5000" target="_blank">Open Main Application</a>
    </div>

    <script>
        function testBasicJS() {
            const result = document.getElementById('js-test-result');
            try {
                result.innerHTML = '<span class="success">✅ JavaScript is working!</span>';
                result.className = 'success';
            } catch (error) {
                result.innerHTML = '<span class="error">❌ JavaScript error: ' + error.message + '</span>';
                result.className = 'error';
            }
        }
        
        function testDOMElements() {
            const result = document.getElementById('dom-test-result');
            const elements = [
                'api-token',
                'step-token', 
                'step-actors',
                'step-input',
                'step-results',
                'loading'
            ];
            
            let html = '';
            let allFound = true;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    html += `✅ ${id}: Found<br>`;
                } else {
                    html += `❌ ${id}: Not found<br>`;
                    allFound = false;
                }
            });
            
            result.innerHTML = html;
            result.className = allFound ? 'success' : 'error';
        }
        
        async function testAPIConnection() {
            const result = document.getElementById('api-test-result');
            const token = document.getElementById('test-token').value.trim();
            
            if (!token) {
                result.innerHTML = '<span class="error">❌ Please enter an API token</span>';
                result.className = 'error';
                return;
            }
            
            result.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/api/test-token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ api_token: token })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<span class="success">✅ API connection successful!</span>';
                    result.className = 'success';
                } else {
                    result.innerHTML = '<span class="error">❌ API error: ' + data.error + '</span>';
                    result.className = 'error';
                }
            } catch (error) {
                result.innerHTML = '<span class="error">❌ Network error: ' + error.message + '</span>';
                result.className = 'error';
            }
        }
        
        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
            testBasicJS();
        });
    </script>
</body>
</html>
