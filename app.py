from flask import Flask, render_template, request, jsonify
from apify_client import ApifyClient

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/test-token', methods=['POST'])
def test_token():
    data = request.get_json()
    if not data or not data.get('api_token'):
        return jsonify({"success": False, "error": "Token required"}), 400

    client = ApifyClient(data['api_token'].strip())
    result = client.test_connection()

    if result["success"]:
        return jsonify({"success": True, "message": "Valid token"})
    return jsonify({"success": False, "error": result["error"]}), 401

@app.route('/api/actors', methods=['POST'])
def get_actors():
    data = request.get_json()
    if not data.get('api_token'):
        return jsonify({"success": False, "error": "Token required"}), 400

    client = ApifyClient(data['api_token'])
    result = client.list_actors()

    if result["success"]:
        return jsonify({"success": True, "actors": result["actors"]})
    return jsonify({"success": False, "error": result["error"]}), 400

@app.route('/api/actor/<actor_id>/schema', methods=['POST'])
def get_actor_schema(actor_id):
    data = request.get_json()
    if not data.get('api_token'):
        return jsonify({"success": False, "error": "Token required"}), 400

    client = ApifyClient(data['api_token'])
    result = client.get_actor_details(actor_id)

    if not result["success"]:
        return jsonify({"success": False, "error": result["error"]}), 400

    actor = result["actor"]
    schema = (actor.get("inputSchema") or
              actor.get("input") or
              actor.get("defaultRunOptions", {}).get("input"))

    if not schema or not schema.get("properties"):
        schema = {
            "type": "object",
            "properties": {"url": {"type": "string", "title": "URL"}},
            "required": ["url"]
        }

    return jsonify({
        "success": True,
        "schema": schema,
        "name": actor.get("name", "Unknown"),
        "description": actor.get("description", "")
    })

@app.route('/api/actor/<actor_id>/run', methods=['POST'])
def run_actor(actor_id):
    data = request.get_json()
    if not data.get('api_token'):
        return jsonify({"success": False, "error": "Token required"}), 400

    client = ApifyClient(data['api_token'])
    result = client.run_actor(actor_id, data.get('input', {}))

    if not result["success"]:
        return jsonify({"success": False, "error": result["error"]}), 400

    run_data = result["run"]
    dataset_items = []

    if run_data.get("defaultDatasetId"):
        dataset_result = client.get_run_dataset(run_data["defaultDatasetId"])
        if dataset_result["success"]:
            dataset_items = dataset_result["items"]

    return jsonify({
        "success": True,
        "run": run_data,
        "results": dataset_items
    })

if __name__ == '__main__':
    app.run(debug=True)
