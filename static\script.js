let currentToken = '';
let selectedActor = null;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Ensure loading is hidden on page load
    hideLoading();

    // Clear any existing status messages
    const statusElements = ['token-status', 'actors-status', 'input-status'];
    statusElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '';
            element.className = 'status';
        }
    });

    console.log('Apify Actor Runner initialized');
});

function showLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.classList.remove('hidden');
    }
}

function hideLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.classList.add('hidden');
    }
}

function showStatus(elementId, message, isError = false) {
    const statusEl = document.getElementById(elementId);
    if (statusEl) {
        statusEl.textContent = message;
        statusEl.className = `status ${isError ? 'error' : 'success'}`;
        console.log(`Status updated: ${elementId} - ${message}`);
    } else {
        console.error(`Status element not found: ${elementId}`);
    }
}

function hideStep(stepId) {
    document.getElementById(stepId).classList.add('hidden');
}

function showStep(stepId) {
    document.getElementById(stepId).classList.remove('hidden');
}

async function validateToken() {
    const token = document.getElementById('api-token').value.trim();
    if (!token) {
        showStatus('token-status', 'Please enter an API token', true);
        return;
    }

    showLoading();
    try {
        const response = await fetch('/api/test-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ api_token: token })
        });

        const data = await response.json();
        
        if (data.success) {
            currentToken = token;
            showStatus('token-status', 'Token validated successfully!');
            setTimeout(() => {
                showStep('step-actors');
                loadActors();
            }, 1000);
        } else {
            showStatus('token-status', `Error: ${data.error}`, true);
        }
    } catch (error) {
        showStatus('token-status', `Network error: ${error.message}`, true);
    } finally {
        hideLoading();
    }
}

async function loadActors() {
    showLoading();
    try {
        const response = await fetch('/api/actors', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ api_token: currentToken })
        });

        const data = await response.json();
        
        if (data.success) {
            displayActors(data.actors);
        } else {
            showStatus('actors-status', `Error loading actors: ${data.error}`, true);
        }
    } catch (error) {
        showStatus('actors-status', `Network error: ${error.message}`, true);
    } finally {
        hideLoading();
    }
}

function displayActors(actors) {
    const container = document.getElementById('actors-list');
    
    if (actors.length === 0) {
        container.innerHTML = '<p>No actors found in your account.</p>';
        return;
    }

    container.innerHTML = actors.map(actor => `
        <div class="actor-card" onclick="selectActor('${actor.id}', '${actor.name}', '${actor.description || ''}')">
            <div class="actor-name">${actor.name}</div>
            <div class="actor-description">${actor.description || 'No description available'}</div>
        </div>
    `).join('');
}

async function selectActor(actorId, actorName, actorDescription) {
    // Visual selection
    document.querySelectorAll('.actor-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.target.closest('.actor-card').classList.add('selected');

    selectedActor = { id: actorId, name: actorName, description: actorDescription };
    
    showLoading();
    try {
        const response = await fetch(`/api/actor/${actorId}/schema`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ api_token: currentToken })
        });

        const data = await response.json();
        
        if (data.success) {
            displayInputForm(data.schema, data.name, data.description);
            showStep('step-input');
        } else {
            showStatus('actors-status', `Error loading schema: ${data.error}`, true);
        }
    } catch (error) {
        showStatus('actors-status', `Network error: ${error.message}`, true);
    } finally {
        hideLoading();
    }
}

function displayInputForm(schema, actorName, actorDescription) {
    document.getElementById('selected-actor-name').textContent = actorName;
    document.getElementById('selected-actor-description').textContent = actorDescription;
    
    const fieldsContainer = document.getElementById('input-fields');
    
    if (!schema || !schema.properties) {
        fieldsContainer.innerHTML = '<p>No input schema available for this actor.</p>';
        return;
    }

    const fields = Object.entries(schema.properties).map(([key, field]) => {
        return createInputField(key, field);
    }).join('');

    fieldsContainer.innerHTML = fields;
}

function createInputField(key, field) {
    const type = field.type || 'string';
    const title = field.title || key;
    const description = field.description || '';
    const required = field.required || false;
    
    let inputHtml = '';
    
    switch (type) {
        case 'boolean':
            inputHtml = `<div class="checkbox-group">
                <input type="checkbox" id="${key}" name="${key}">
                <label for="${key}">${title}</label>
            </div>`;
            break;
        case 'number':
        case 'integer':
            inputHtml = `<input type="number" id="${key}" name="${key}" placeholder="${title}">`;
            break;
        case 'array':
            inputHtml = `<textarea id="${key}" name="${key}" placeholder="Enter items separated by commas"></textarea>`;
            break;
        default:
            if (field.enum) {
                const options = field.enum.map(option => `<option value="${option}">${option}</option>`).join('');
                inputHtml = `<select id="${key}" name="${key}"><option value="">Select ${title}</option>${options}</select>`;
            } else {
                inputHtml = `<input type="text" id="${key}" name="${key}" placeholder="${title}">`;
            }
    }
    
    return `
        <div class="field-group">
            <label class="field-label">${title}${required ? ' *' : ''}</label>
            ${description ? `<div class="field-description">${description}</div>` : ''}
            ${inputHtml}
        </div>
    `;
}

document.getElementById('input-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const inputData = {};
    
    for (const [key, value] of formData.entries()) {
        const field = document.getElementById(key);
        if (field.type === 'checkbox') {
            inputData[key] = field.checked;
        } else if (field.type === 'number') {
            inputData[key] = value ? Number(value) : undefined;
        } else if (field.tagName === 'TEXTAREA' && key.includes('array')) {
            inputData[key] = value ? value.split(',').map(item => item.trim()) : [];
        } else {
            inputData[key] = value || undefined;
        }
    }
    
    // Remove undefined values
    Object.keys(inputData).forEach(key => {
        if (inputData[key] === undefined || inputData[key] === '') {
            delete inputData[key];
        }
    });
    
    await runActor(inputData);
});

async function runActor(inputData) {
    showLoading();
    try {
        const response = await fetch(`/api/actor/${selectedActor.id}/run`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                api_token: currentToken,
                input: inputData
            })
        });

        const data = await response.json();
        
        if (data.success) {
            displayResults(data.run, data.results);
            showStep('step-results');
        } else {
            showStatus('input-status', `Error running actor: ${data.error}`, true);
        }
    } catch (error) {
        showStatus('input-status', `Network error: ${error.message}`, true);
    } finally {
        hideLoading();
    }
}

function displayResults(runData, results) {
    const runInfo = document.getElementById('run-info');
    runInfo.innerHTML = `
        <h4>Run Information</h4>
        <p><strong>Status:</strong> ${runData.status}</p>
        <p><strong>Started:</strong> ${new Date(runData.startedAt).toLocaleString()}</p>
        <p><strong>Finished:</strong> ${runData.finishedAt ? new Date(runData.finishedAt).toLocaleString() : 'N/A'}</p>
        <p><strong>Duration:</strong> ${runData.stats?.runTimeSecs ? runData.stats.runTimeSecs + 's' : 'N/A'}</p>
    `;
    
    const resultsData = document.getElementById('results-data');
    if (results && results.length > 0) {
        resultsData.textContent = JSON.stringify(results, null, 2);
    } else {
        resultsData.textContent = 'No results returned from the actor run.';
    }
}

function startOver() {
    currentToken = '';
    selectedActor = null;
    
    // Reset form
    document.getElementById('api-token').value = '';
    
    // Hide all steps except the first
    hideStep('step-actors');
    hideStep('step-input');
    hideStep('step-results');
    
    // Clear status messages
    document.getElementById('token-status').textContent = '';
    document.getElementById('actors-status').textContent = '';
    document.getElementById('input-status').textContent = '';
}
