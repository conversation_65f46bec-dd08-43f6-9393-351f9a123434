# Apify Actor Runner

A simple web application that allows users to run their Apify actors through an intuitive web interface. Built with Flask (Python) backend and vanilla JavaScript frontend.

## Features

- **API Key Authentication**: Secure authentication with your Apify API token
- **Dynamic Actor Discovery**: Automatically fetches and displays your available actors
- **Runtime Schema Loading**: Dynamically generates input forms based on each actor's schema
- **Single-Run Execution**: Execute actors with custom inputs and view results immediately
- **Error Handling**: Comprehensive error handling with user-friendly feedback
- **Minimalistic Design**: Clean, responsive interface focused on functionality

## Quick Start

### Prerequisites

- Python 3.7 or higher
- Apify account with API token ([Get yours here](https://console.apify.com/account#/integrations))

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd apify-actor-runner
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser**
   Navigate to `http://localhost:5000`

## How to Use

### Step 1: Enter API Token
- Get your Apify API token from [Apify Console](https://console.apify.com/account#/integrations)
- Enter the token in the web interface
- Click "Validate Token" to verify it works

### Step 2: Select an Actor
- Browse through your available actors
- Click on any actor card to select it
- The app will automatically load the actor's input schema

### Step 3: Configure Input
- Fill out the dynamically generated form based on the actor's schema
- All field types are supported: text, numbers, booleans, arrays, enums
- Required fields are marked with an asterisk (*)

### Step 4: View Results
- Click "Run Actor" to execute
- View run information (status, duration, etc.)
- See the dataset results returned by the actor
- Click "Start Over" to run another actor

## Project Structure

```
apify-actor-runner/
├── app.py                 # Flask application with API routes
├── apify_client.py        # Apify API client wrapper
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables (optional)
├── templates/
│   └── index.html        # Main HTML template
├── static/
│   ├── style.css         # CSS styles
│   └── script.js         # JavaScript functionality
└── docs/
    ├── API.md            # API documentation
    ├── ARCHITECTURE.md   # Architecture overview
    └── TROUBLESHOOTING.md # Common issues and solutions
```

## API Endpoints

- `GET /` - Main application interface
- `POST /api/test-token` - Validate API token
- `POST /api/actors` - Get list of user's actors
- `POST /api/actor/<id>/schema` - Get actor input schema
- `POST /api/actor/<id>/run` - Run actor with input

## Testing Actor

For testing purposes, you can use any of your existing actors. Popular choices include:
- Web scrapers (e.g., website content extractors)
- Data processors
- API integrators

## Design Choices

### Minimalistic Approach
- **Single-page application**: All functionality in one interface
- **No external frameworks**: Pure HTML/CSS/JavaScript for frontend
- **Simple Flask backend**: Minimal routes focused on core functionality
- **Direct API integration**: No unnecessary abstractions

### Security Considerations
- API tokens are handled securely (not stored server-side)
- Input validation on both frontend and backend
- Error messages don't expose sensitive information
- HTTPS recommended for production use

### User Experience
- **Progressive disclosure**: Steps are revealed as user progresses
- **Visual feedback**: Loading indicators and status messages
- **Error recovery**: Clear error messages with actionable guidance
- **Responsive design**: Works on desktop and mobile devices

## Environment Variables

You can optionally set environment variables in `.env`:

```bash
# Optional: Set a default API token (not recommended for production)
APIFY_API_TOKEN=your_token_here
```

## Dependencies

- **Flask 3.0.0**: Web framework
- **requests 2.31.0**: HTTP client for Apify API
- **python-dotenv 1.0.0**: Environment variable management

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Contributing

This is a demonstration project. For improvements:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues related to:
- **Apify API**: Check [Apify Documentation](https://docs.apify.com)
- **This application**: See `docs/TROUBLESHOOTING.md`
- **Actor-specific issues**: Consult the actor's documentation

---

**Note**: This application is designed for demonstration purposes. For production use, consider adding authentication, rate limiting, and enhanced security measures.
