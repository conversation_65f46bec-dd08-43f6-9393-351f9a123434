import requests

class ApifyClient:
    def __init__(self, api_token):
        self.token = api_token.strip()
        self.base_url = "https://api.apify.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

    def test_connection(self):
        try:
            r = requests.get(f"{self.base_url}/users/me", headers=self.headers, timeout=10)
            if r.status_code == 200:
                return {"success": True}
            return {"success": False, "error": f"HTTP {r.status_code}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def list_actors(self):
        try:
            r = requests.get(f"{self.base_url}/acts", headers=self.headers, timeout=10)
            if r.status_code == 200:
                actors = r.json().get("data", {}).get("items", [])
                return {"success": True, "actors": actors}
            return {"success": False, "error": f"HTTP {r.status_code}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_actor_details(self, actor_id):
        try:
            r = requests.get(f"{self.base_url}/acts/{actor_id}", headers=self.headers, timeout=10)
            if r.status_code != 200:
                return {"success": False, "error": f"HTTP {r.status_code}"}

            actor = r.json().get("data", {})

            try:
                schema_r = requests.get(f"{self.base_url}/acts/{actor_id}/versions/latest",
                                     headers=self.headers, timeout=10)
                if schema_r.status_code == 200:
                    schema = schema_r.json().get("data", {}).get("inputSchema")
                    if schema:
                        actor["inputSchema"] = schema
            except:
                pass

            return {"success": True, "actor": actor}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def run_actor(self, actor_id, input_data):
        try:
            r = requests.post(f"{self.base_url}/acts/{actor_id}/runs",
                            headers=self.headers, json=input_data,
                            params={"waitForFinish": 120}, timeout=130)

            if r.status_code == 201:
                return {"success": True, "run": r.json().get("data", {})}
            return {"success": False, "error": f"HTTP {r.status_code}: {r.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_run_dataset(self, dataset_id):
        try:
            r = requests.get(f"{self.base_url}/datasets/{dataset_id}/items",
                           headers=self.headers, timeout=10)
            if r.status_code == 200:
                return {"success": True, "items": r.json()}
            return {"success": False, "error": f"HTTP {r.status_code}"}
        except Exception as e:
            return {"success": False, "error": str(e)}