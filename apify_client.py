import requests
import json
from typing import Dict, List, Optional, Any

class ApifyClient:
    """Simple Apify API client for actor operations"""
    
    def __init__(self, api_token: str):
        if not api_token or not api_token.strip():
            raise ValueError("API token cannot be empty")

        self.api_token = api_token.strip()
        self.base_url = "https://api.apify.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """Test if the API token is valid"""
        try:
            response = requests.get(
                f"{self.base_url}/users/me",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def list_actors(self) -> Dict[str, Any]:
        """Get list of user's actors"""
        try:
            response = requests.get(
                f"{self.base_url}/acts",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                data = response.json()
                actors = data.get("data", {}).get("items", [])
                return {"success": True, "actors": actors}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_actor_details(self, actor_id: str) -> Dict[str, Any]:
        """Get actor details including input schema"""
        try:
            response = requests.get(
                f"{self.base_url}/acts/{actor_id}",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                data = response.json()
                actor_data = data.get("data", {})
                return {"success": True, "actor": actor_data}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def run_actor(self, actor_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run an actor with given input and wait for completion"""
        try:
            # Start the actor run
            response = requests.post(
                f"{self.base_url}/acts/{actor_id}/runs",
                headers=self.headers,
                json=input_data,
                params={"waitForFinish": 120},  # Wait up to 2 minutes
                timeout=130
            )
            
            if response.status_code == 201:
                data = response.json()
                run_data = data.get("data", {})
                return {"success": True, "run": run_data}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_run_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """Get dataset items from a completed run"""
        try:
            response = requests.get(
                f"{self.base_url}/datasets/{dataset_id}/items",
                headers=self.headers,
                timeout=10
            )
            if response.status_code == 200:
                items = response.json()
                return {"success": True, "items": items}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
